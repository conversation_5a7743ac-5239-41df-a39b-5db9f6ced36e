import { Alert } from 'react-native';
import { realChatService } from './realChatService';
import { ChatItem, BulkActionOptions } from '../types/chatManagement';

export class ChatManagementService {
  
  /**
   * Convert raw chat data to ChatItem format
   */
  static convertChatData(chatData: any, userId: string): ChatItem {
    return {
      id: chatData.id,
      name: chatData.isGroup ? chatData.groupName || 'Group Chat' : chatData.participantName || 'Unknown',
      avatar: chatData.isGroup ? chatData.groupAvatar : chatData.participantAvatar,
      lastMessage: chatData.lastMessage?.text || '',
      timestamp: chatData.updatedAt ? new Date(chatData.updatedAt).toLocaleString() : '',
      unreadCount: 0, // Default to 0 since unreadCount is not in ChatData
      isGroup: chatData.isGroup,
      participants: chatData.participantIds,
      isArchived: chatData.archivedBy?.includes(userId) || false,
      isPinned: false, // Add pinned logic if needed
      isMuted: false, // Add muted logic if needed
    };
  }

  /**
   * Load user chats and convert to ChatItem format
   */
  static async loadUserChats(userId: string): Promise<{ success: boolean; chats?: ChatItem[]; error?: string }> {
    try {
      const result = await realChatService.getUserChats(userId);
      if (result.success && result.chats) {
        const chatItems = result.chats.map(chat => this.convertChatData(chat, userId));
        return { success: true, chats: chatItems };
      } else {
        return { success: false, error: result.error || 'Failed to load chats' };
      }
    } catch (error) {
      console.error('Error loading chats:', error);
      return { success: false, error: 'Failed to load chats' };
    }
  }

  /**
   * Archive multiple chats
   */
  static async archiveChats(chatIds: string[], userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await Promise.all(chatIds.map(id => realChatService.archiveChat(id, userId)));
      return { success: true };
    } catch (error) {
      console.error('Error archiving chats:', error);
      return { success: false, error: 'Failed to archive chats' };
    }
  }

  /**
   * Delete multiple chats
   */
  static async deleteChats(chatIds: string[], options: BulkActionOptions): Promise<{ success: boolean; error?: string }> {
    try {
      await realChatService.clearMultipleChats(chatIds, options);
      return { success: true };
    } catch (error) {
      console.error('Error deleting chats:', error);
      return { success: false, error: 'Failed to delete chats' };
    }
  }

  /**
   * Mute multiple chats (placeholder implementation)
   */
  static async muteChats(chatIds: string[]): Promise<{ success: boolean; error?: string }> {
    try {
      // For now, just return success - mute functionality can be implemented later
      // await Promise.all(chatIds.map(id => realChatService.muteChat(id)));
      return { success: true };
    } catch (error) {
      console.error('Error muting chats:', error);
      return { success: false, error: 'Failed to mute chats' };
    }
  }

  /**
   * Show confirmation dialog for bulk delete
   */
  static showDeleteConfirmation(
    count: number, 
    onConfirm: () => void
  ): void {
    Alert.alert(
      'Delete Chats',
      `Are you sure you want to delete ${count} chat(s)? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: onConfirm
        }
      ]
    );
  }

  /**
   * Show success message
   */
  static showSuccessMessage(action: string, count: number): void {
    Alert.alert('Success', `${count} chat(s) ${action}`);
  }

  /**
   * Show error message
   */
  static showErrorMessage(message: string): void {
    Alert.alert('Error', message);
  }

  /**
   * Filter chats based on search query and filter type
   */
  static filterChats(
    chats: ChatItem[], 
    searchQuery: string, 
    filterType: 'all' | 'individual' | 'group' | 'archived'
  ): ChatItem[] {
    return chats.filter(chat => {
      // Search filter
      const matchesSearch = chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           chat.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (!matchesSearch) return false;

      // Type filter
      switch (filterType) {
        case 'individual':
          return !chat.isGroup;
        case 'group':
          return chat.isGroup;
        case 'archived':
          return chat.isArchived;
        default:
          return !chat.isArchived; // 'all' shows non-archived chats
      }
    });
  }

  /**
   * Sort chats based on sort type
   */
  static sortChats(chats: ChatItem[], sortBy: 'recent' | 'name' | 'unread'): ChatItem[] {
    return [...chats].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'unread':
          return (b.unreadCount || 0) - (a.unreadCount || 0);
        case 'recent':
        default:
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          
          const aTime = new Date(a.timestamp || 0).getTime();
          const bTime = new Date(b.timestamp || 0).getTime();
          return bTime - aTime;
      }
    });
  }
}
