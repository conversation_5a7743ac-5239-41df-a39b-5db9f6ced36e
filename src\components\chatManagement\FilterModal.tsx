import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FilterType, SortType, FilterOption, SortOption } from '../../types/chatManagement';

interface FilterModalProps {
  visible: boolean;
  filterType: FilterType;
  sortBy: SortType;
  onClose: () => void;
  onFilterChange: (filter: FilterType) => void;
  onSortChange: (sort: SortType) => void;
}

const { height: screenHeight } = Dimensions.get('window');

const FILTER_OPTIONS: FilterOption[] = [
  { key: 'all', label: 'All Chats', icon: 'chatbubbles' },
  { key: 'individual', label: 'Individual', icon: 'person' },
  { key: 'group', label: 'Groups', icon: 'people' },
  { key: 'archived', label: 'Archived', icon: 'archive' },
];

const SORT_OPTIONS: SortOption[] = [
  { key: 'recent', label: 'Most Recent', icon: 'time' },
  { key: 'name', label: 'Name (A-Z)', icon: 'text' },
  { key: 'unread', label: 'Unread Count', icon: 'notifications' },
];

export const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  filterType,
  sortBy,
  onClose,
  onFilterChange,
  onSortChange,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
      }}>
        <View style={{
          backgroundColor: '#FFFFFF',
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 40,
          maxHeight: screenHeight * 0.7,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 24,
          }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#333' }}>
              Filter & Sort
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Filter Type */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', marginBottom: 12 }}>
              Chat Type
            </Text>
            {FILTER_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.key}
                onPress={() => onFilterChange(option.key)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  backgroundColor: filterType === option.key ? '#F0F8FF' : 'transparent',
                  marginBottom: 8,
                }}
              >
                <Ionicons
                  name={option.icon as any}
                  size={20}
                  color={filterType === option.key ? '#87CEEB' : '#666'}
                />
                <Text style={{
                  marginLeft: 12,
                  fontSize: 16,
                  color: filterType === option.key ? '#87CEEB' : '#333',
                  fontWeight: filterType === option.key ? '600' : 'normal',
                }}>
                  {option.label}
                </Text>
                {filterType === option.key && (
                  <Ionicons name="checkmark" size={20} color="#87CEEB" style={{ marginLeft: 'auto' }} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Sort By */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#333', marginBottom: 12 }}>
              Sort By
            </Text>
            {SORT_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.key}
                onPress={() => onSortChange(option.key)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  backgroundColor: sortBy === option.key ? '#F0F8FF' : 'transparent',
                  marginBottom: 8,
                }}
              >
                <Ionicons
                  name={option.icon as any}
                  size={20}
                  color={sortBy === option.key ? '#87CEEB' : '#666'}
                />
                <Text style={{
                  marginLeft: 12,
                  fontSize: 16,
                  color: sortBy === option.key ? '#87CEEB' : '#333',
                  fontWeight: sortBy === option.key ? '600' : 'normal',
                }}>
                  {option.label}
                </Text>
                {sortBy === option.key && (
                  <Ionicons name="checkmark" size={20} color="#87CEEB" style={{ marginLeft: 'auto' }} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Apply Button */}
          <TouchableOpacity
            onPress={onClose}
            style={{
              backgroundColor: '#87CEEB',
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: '#FFFFFF',
              fontSize: 16,
              fontWeight: '600',
            }}>
              Apply Filters
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
