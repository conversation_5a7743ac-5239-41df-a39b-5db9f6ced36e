import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface BulkActionsProps {
  selectedCount: number;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onArchive: () => void;
  onMute: () => void;
  onDelete: () => void;
}

export const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  onSelectAll,
  onClearSelection,
  onArchive,
  onMute,
  onDelete,
}) => {
  return (
    <View style={{
      backgroundColor: '#FFFFFF',
      paddingHorizontal: 20,
      paddingVertical: 12,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    }}>
      <Text style={{ fontSize: 16, fontWeight: '600', color: '#333' }}>
        {selectedCount} selected
      </Text>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          onPress={onSelectAll}
          style={{ marginRight: 16 }}
        >
          <Text style={{ color: '#87CEEB', fontSize: 14, fontWeight: '600' }}>
            Select All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onClearSelection}
          style={{ marginRight: 16 }}
        >
          <Text style={{ color: '#666', fontSize: 14 }}>
            Clear
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onArchive}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="archive" size={20} color="#87CEEB" />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onMute}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="volume-mute" size={20} color="#87CEEB" />
        </TouchableOpacity>
        <TouchableOpacity onPress={onDelete}>
          <Ionicons name="trash" size={20} color="#FF6B6B" />
        </TouchableOpacity>
      </View>
    </View>
  );
};
