import { useMemo } from 'react';
import { ChatItem, FilterType, SortType } from '../types/chatManagement';

interface UseChatFilteringProps {
  chats: ChatItem[];
  searchQuery: string;
  filterType: FilterType;
  sortBy: SortType;
}

export const useChatFiltering = ({ 
  chats, 
  searchQuery, 
  filterType, 
  sortBy 
}: UseChatFilteringProps) => {
  
  const filteredAndSortedChats = useMemo(() => {
    let filteredChats = chats.filter(chat => {
      // Search filter
      const matchesSearch = chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           chat.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (!matchesSearch) return false;

      // Type filter
      switch (filterType) {
        case 'individual':
          return !chat.isGroup;
        case 'group':
          return chat.isGroup;
        case 'archived':
          return chat.isArchived;
        default:
          return !chat.isArchived; // 'all' shows non-archived chats
      }
    });

    // Sort chats
    filteredChats.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'unread':
          return (b.unreadCount || 0) - (a.unreadCount || 0);
        case 'recent':
        default:
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          
          const aTime = new Date(a.timestamp || 0).getTime();
          const bTime = new Date(b.timestamp || 0).getTime();
          return bTime - aTime;
      }
    });

    return filteredChats;
  }, [chats, searchQuery, filterType, sortBy]);

  return {
    filteredChats: filteredAndSortedChats,
    totalCount: chats.length,
    filteredCount: filteredAndSortedChats.length,
  };
};
