// 🚀 ULTIMATE INDIVIDUAL CHAT ROOM
// Complete individual messaging experience with real Firebase functionality
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { navigationService, ROUTES } from '../services/navigationService';
import { User } from '../types';
import { realTimeMessagingService } from '../services/realTimeMessagingService';
import * as Haptics from 'expo-haptics';

// Import chat components
import { ChatHeader } from './chat/ChatHeader';
import { MessageList } from './chat/MessageList';
import { MessageInput } from './chat/MessageInput';
import { ChatActions } from './chat/ChatActions';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',
  background: '#f8f9fa',
  text: '#333',
  white: '#ffffff',
};

// Simplified Message Interface
interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: any;
  type?: 'text' | 'image' | 'video' | 'voice' | 'file';
  mediaUrl?: string;
  replyTo?: string;
  isEdited?: boolean;
  readBy?: string[];
}

interface TypingUser {
  id: string;
  name: string;
}

interface UltimateIndividualChatRoomProps {
  chatId: string;
  partnerId: string;
  partnerName: string;
  partnerAvatar?: string;
  currentUser?: User;
  currentUserId?: string;
  isOnline?: boolean;
  onBack?: () => void;
}

export const UltimateIndividualChatRoom: React.FC<UltimateIndividualChatRoomProps> = ({
  chatId,
  partnerId,
  partnerName,
  partnerAvatar,
  currentUser,
  _currentUserId,
  isOnline = false,
  onBack,
}) => {
  const router = useRouter();
  
  // ==================== STATE MANAGEMENT ====================
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<ChatMessage | null>(null);
  const [showActions, setShowActions] = useState(false);

  // ==================== ESSENTIAL FUNCTIONS ====================

  const loadMessages = useCallback(async () => {
    try {
      console.log('🔥 Loading real messages from Firebase for chat:', chatId);
      
      const unsubscribe = realTimeMessagingService.subscribeToMessages(
        chatId,
        (firebaseMessages) => {
          console.log('📨 Received real-time messages:', firebaseMessages.length);
          setMessages(firebaseMessages);
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    }
  }, [chatId]);

  const setupTypingIndicators = useCallback(() => {
    const unsubscribe = realTimeMessagingService.subscribeToTypingIndicators(
      chatId,
      currentUser?.id || '',
      (users) => {
        setTypingUsers(users.map(user => ({
          id: user.userId,
          name: user.userName,
        })));
      }
    );
    return unsubscribe;
  }, [chatId, currentUser?.id]);

  useEffect(() => {
    loadMessages();
    setupTypingIndicators();
  }, [loadMessages, setupTypingIndicators]);

  // ==================== MESSAGE HANDLERS ====================

  const handleSendMessage = async (text: string) => {
    try {
      await realTimeMessagingService.sendMessage(
        chatId,
        currentUser?.id || '',
        currentUser?.displayName || 'You',
        text,
        'text'
      );
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('❌ Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  const handleSendVoiceMessage = async (audioUri: string, duration: number) => {
    try {
      await realTimeMessagingService.sendMessage(
        chatId,
        currentUser?.id || '',
        currentUser?.displayName || 'You',
        `Voice message (${duration}s)`,
        'voice',
        audioUri
      );
    } catch (error) {
      console.error('❌ Error sending voice message:', error);
      Alert.alert('Error', 'Failed to send voice message');
    }
  };

  const handleSendMedia = async (mediaUri: string, type: 'image' | 'video') => {
    try {
      await realTimeMessagingService.sendMessage(
        chatId,
        currentUser?.id || '',
        currentUser?.displayName || 'You',
        `${type} message`,
        type,
        mediaUri
      );
    } catch (error) {
      console.error('❌ Error sending media:', error);
      Alert.alert('Error', 'Failed to send media');
    }
  };

  const handleTypingStart = () => {
    setIsTyping(true);
    realTimeMessagingService.startTyping(chatId, currentUser?.id || '');
  };

  const handleTypingStop = () => {
    setIsTyping(false);
    realTimeMessagingService.stopTyping(chatId, currentUser?.id || '');
  };

  const handleMessagePress = (message: ChatMessage) => {
    if (isSelectionMode) {
      handleSelectMessage(message.id);
    }
  };

  const handleMessageLongPress = (message: ChatMessage) => {
    setSelectedMessage(message);
    setShowActions(true);
  };

  const handleSelectMessage = (messageId: string) => {
    if (selectedMessages.includes(messageId)) {
      setSelectedMessages(prev => prev.filter(id => id !== messageId));
    } else {
      setSelectedMessages(prev => [...prev, messageId]);
    }
  };

  const handleReply = (message: ChatMessage) => {
    setReplyingTo(message);
  };

  const handleEdit = (message: ChatMessage) => {
    // Edit functionality
    console.log('Edit message:', message.id);
  };

  const handleDelete = (messageId: string) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await realTimeMessagingService.deleteMessage(messageId);
            } catch (error) {
              console.error('❌ Error deleting message:', error);
              Alert.alert('Error', 'Failed to delete message');
            }
          },
        },
      ]
    );
  };

  const handleForward = (message: ChatMessage) => {
    console.log('Forward message:', message.id);
  };

  const handleCancelReply = () => {
    setReplyingTo(null);
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  const handleUserPress = () => {
    // Navigate to user profile
    console.log('User pressed:', partnerId);
  };

  const handleVoiceCall = () => {
    navigationService.navigate(ROUTES.VOICE_CALL, { userId: partnerId });
  };

  const handleVideoCall = () => {
    navigationService.navigate(ROUTES.VIDEO_CALL, { userId: partnerId });
  };

  const handleMoreOptions = () => {
    console.log('More options pressed');
  };

  const handleCancelSelection = () => {
    setIsSelectionMode(false);
    setSelectedMessages([]);
  };

  const handleDeleteSelected = () => {
    Alert.alert(
      'Delete Messages',
      `Are you sure you want to delete ${selectedMessages.length} message${selectedMessages.length !== 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              for (const messageId of selectedMessages) {
                await realTimeMessagingService.deleteMessage(messageId);
              }
              setIsSelectionMode(false);
              setSelectedMessages([]);
            } catch (error) {
              console.error('❌ Error deleting messages:', error);
              Alert.alert('Error', 'Failed to delete messages');
            }
          },
        },
      ]
    );
  };

  const handleForwardSelected = () => {
    console.log('Forward selected messages:', selectedMessages);
  };

  // ==================== RENDER ====================

  const chatUser = {
    id: partnerId,
    name: partnerName,
    avatar: partnerAvatar,
    isOnline: isOnline,
  };

  return (
    <View style={styles.container}>
      <ChatHeader
        user={chatUser}
        onBack={handleBack}
        onUserPress={handleUserPress}
        onVoiceCall={handleVoiceCall}
        onVideoCall={handleVideoCall}
        onMoreOptions={handleMoreOptions}
        isSelectionMode={isSelectionMode}
        selectedCount={selectedMessages.length}
        onCancelSelection={handleCancelSelection}
        onDeleteSelected={handleDeleteSelected}
        onForwardSelected={handleForwardSelected}
      />

      <KeyboardAvoidingView
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <MessageList
          messages={messages}
          currentUserId={currentUser?.id || ''}
          typingUsers={typingUsers}
          onMessagePress={handleMessagePress}
          onMessageLongPress={handleMessageLongPress}
          onReply={handleReply}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onForward={handleForward}
          isSelectionMode={isSelectionMode}
          selectedMessages={selectedMessages}
          onSelectMessage={handleSelectMessage}
        />

        <MessageInput
          onSendMessage={handleSendMessage}
          onSendVoiceMessage={handleSendVoiceMessage}
          onSendMedia={handleSendMedia}
          isTyping={isTyping}
          onTypingStart={handleTypingStart}
          onTypingStop={handleTypingStop}
          replyingTo={replyingTo}
          onCancelReply={handleCancelReply}
        />
      </KeyboardAvoidingView>

      <ChatActions
        visible={showActions}
        onClose={() => setShowActions(false)}
        message={selectedMessage}
        isOwnMessage={selectedMessage?.senderId === currentUser?.id}
        onReply={() => {
          if (selectedMessage) handleReply(selectedMessage);
        }}
        onEdit={() => {
          if (selectedMessage) handleEdit(selectedMessage);
        }}
        onDelete={() => {
          if (selectedMessage) handleDelete(selectedMessage.id);
        }}
        onForward={() => {
          if (selectedMessage) handleForward(selectedMessage);
        }}
        onCopy={() => {}}
        onShare={() => {}}
        onInfo={() => {}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  chatContainer: {
    flex: 1,
  },
});
