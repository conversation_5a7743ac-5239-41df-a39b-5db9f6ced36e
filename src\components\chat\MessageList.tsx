// 🚀 MESSAGE LIST COMPONENT
// Displays chat messages with real-time updates

import React, { useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';

interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: any;
  type?: 'text' | 'image' | 'video' | 'voice' | 'file';
  mediaUrl?: string;
  replyTo?: string;
  isEdited?: boolean;
  readBy?: string[];
}

interface TypingUser {
  id: string;
  name: string;
}

interface MessageListProps {
  messages: ChatMessage[];
  currentUserId: string;
  typingUsers: TypingUser[];
  onMessagePress: (_message: ChatMessage) => void;
  onMessageLongPress: (_message: ChatMessage) => void;
  onReply: (_message: ChatMessage) => void;
  onEdit: (_message: ChatMessage) => void;
  onDelete: (_messageId: string) => void;
  onForward: (_message: ChatMessage) => void;
  isSelectionMode: boolean;
  selectedMessages: string[];
  onSelectMessage: (_messageId: string) => void;
}

const COLORS = {
  background: '#f8f9fa',
  white: '#ffffff',
};

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  currentUserId,
  typingUsers,
  onMessagePress,
  onMessageLongPress,
  onReply,
  onEdit,
  onDelete,
  onForward,
  isSelectionMode,
  selectedMessages,
  onSelectMessage,
}) => {
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    const isOwnMessage = item.senderId === currentUserId;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    
    const showSenderName = !isOwnMessage && (
      !previousMessage || 
      previousMessage.senderId !== item.senderId
    );
    
    const showTimestamp = !nextMessage || 
      nextMessage.senderId !== item.senderId ||
      (nextMessage.timestamp?.toDate?.() || new Date(nextMessage.timestamp)).getTime() - 
      (item.timestamp?.toDate?.() || new Date(item.timestamp)).getTime() > 300000; // 5 minutes

    return (
      <MessageBubble
        message={item}
        isOwnMessage={isOwnMessage}
        showSenderName={showSenderName}
        showTimestamp={showTimestamp}
        onPress={() => onMessagePress(item)}
        onLongPress={() => onMessageLongPress(item)}
        _onReply={() => onReply(item)}
        _onEdit={() => onEdit(item)}
        _onDelete={() => onDelete(item.id)}
        _onForward={() => onForward(item)}
        isSelectionMode={isSelectionMode}
        isSelected={selectedMessages.includes(item.id)}
        onSelect={() => onSelectMessage(item.id)}
      />
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;
    
    return (
      <View style={styles.typingContainer}>
        <TypingIndicator users={typingUsers} />
      </View>
    );
  };

  const getItemLayout = (_: any, index: number) => ({
    length: 80, // Estimated height
    offset: 80 * index,
    index,
  });

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={20}
        ListFooterComponent={renderTypingIndicator}
        onScrollToIndexFailed={(info) => {
          console.warn('Scroll to index failed:', info);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    minHeight: SCREEN_HEIGHT * 0.6,
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
