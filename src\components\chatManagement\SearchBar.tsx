import React from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearSearch: () => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  searchQuery,
  onSearchChange,
  onClearSearch,
}) => {
  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 25,
      paddingHorizontal: 16,
      paddingVertical: 12,
    }}>
      <Ionicons name="search" size={20} color="#FFFFFF" />
      <TextInput
        style={{
          flex: 1,
          marginLeft: 12,
          fontSize: 16,
          color: '#FFFFFF',
        }}
        placeholder="Search chats..."
        placeholderTextColor="rgba(255, 255, 255, 0.7)"
        value={searchQuery}
        onChangeText={onSearchChange}
      />
      {searchQuery.length > 0 && (
        <TouchableOpacity onPress={onClearSearch}>
          <Ionicons name="close-circle" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </View>
  );
};
