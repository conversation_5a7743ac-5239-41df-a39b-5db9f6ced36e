// Chat Management Types
export interface ChatItem {
  id: string;
  name: string;
  avatar?: string;
  lastMessage?: string;
  timestamp?: string;
  unreadCount?: number;
  isGroup?: boolean;
  participants?: string[];
  isArchived?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
}

export type FilterType = 'all' | 'individual' | 'group' | 'archived';
export type SortType = 'recent' | 'name' | 'unread';

export interface ChatManagementState {
  chats: ChatItem[];
  loading: boolean;
  selectedChats: Set<string>;
  searchQuery: string;
  filterType: FilterType;
  sortBy: SortType;
  showBulkActions: boolean;
  showFilterModal: boolean;
  refreshing: boolean;
}

export interface BulkActionOptions {
  clearMessages: boolean;
  clearMedia: boolean;
  clearAll: boolean;
}

export interface FilterOption {
  key: FilterType;
  label: string;
  icon: string;
}

export interface SortOption {
  key: SortType;
  label: string;
  icon: string;
}

export interface ChatManagementActions {
  loadChats: () => Promise<void>;
  handleRefresh: () => Promise<void>;
  toggleChatSelection: (chatId: string) => void;
  selectAllChats: () => void;
  clearSelection: () => void;
  handleBulkArchive: () => Promise<void>;
  handleBulkDelete: () => Promise<void>;
  handleBulkMute: () => Promise<void>;
  setSearchQuery: (query: string) => void;
  setFilterType: (type: FilterType) => void;
  setSortBy: (sort: SortType) => void;
  setShowFilterModal: (show: boolean) => void;
}
