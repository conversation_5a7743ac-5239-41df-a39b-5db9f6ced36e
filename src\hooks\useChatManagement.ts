import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { realChatService } from '../services/realChatService';
import { 
  ChatItem, 
  ChatManagementState, 
  FilterType, 
  SortType,
  ChatManagementActions 
} from '../types/chatManagement';

export const useChatManagement = (): ChatManagementState & ChatManagementActions => {
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('recent');
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const user = useSelector((state: RootState) => state.user.currentUser);

  const loadChats = useCallback(async () => {
    try {
      setLoading(true);
      const result = await realChatService.getUserChats(user?.id || '');
      if (result.success && result.chats) {
        // Convert ChatData to ChatItem format
        const chatItems: ChatItem[] = result.chats.map(chat => ({
          id: chat.id,
          name: chat.isGroup ? chat.groupName || 'Group Chat' : chat.participantName || 'Unknown',
          avatar: chat.isGroup ? chat.groupAvatar : chat.participantAvatar,
          lastMessage: chat.lastMessage?.text || '',
          timestamp: chat.updatedAt ? new Date(chat.updatedAt).toLocaleString() : '',
          unreadCount: 0, // Default to 0 since unreadCount is not in ChatData
          isGroup: chat.isGroup,
          participants: chat.participantIds,
          isArchived: (chat as any).archivedBy?.includes(user?.id) || false, // Check if current user archived this chat
          isPinned: false, // Add pinned logic if needed
          isMuted: false, // Add muted logic if needed
        }));
        setChats(chatItems);
      } else {
        console.error('Failed to load chats:', result.error);
        Alert.alert('Error', result.error || 'Failed to load chats');
      }
    } catch (error) {
      console.error('Error loading chats:', error);
      Alert.alert('Error', 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadChats();
    setRefreshing(false);
  }, [loadChats]);

  const toggleChatSelection = useCallback((chatId: string) => {
    const newSelected = new Set(selectedChats);
    if (newSelected.has(chatId)) {
      newSelected.delete(chatId);
    } else {
      newSelected.add(chatId);
    }
    setSelectedChats(newSelected);
    setShowBulkActions(newSelected.size > 0);
  }, [selectedChats]);

  const selectAllChats = useCallback(() => {
    // This will be implemented with filtering logic
    const allChatIds = chats.map(chat => chat.id);
    setSelectedChats(new Set(allChatIds));
    setShowBulkActions(true);
  }, [chats]);

  const clearSelection = useCallback(() => {
    setSelectedChats(new Set());
    setShowBulkActions(false);
  }, []);

  const handleBulkArchive = useCallback(async () => {
    try {
      const chatIds = Array.from(selectedChats);
      await Promise.all(chatIds.map(id => realChatService.archiveChat(id, user?.id || '')));
      await loadChats();
      clearSelection();
      Alert.alert('Success', `${chatIds.length} chat(s) archived`);
    } catch (error) {
      Alert.alert('Error', 'Failed to archive chats');
    }
  }, [selectedChats, user?.id, loadChats, clearSelection]);

  const handleBulkDelete = useCallback(async () => {
    Alert.alert(
      'Delete Chats',
      `Are you sure you want to delete ${selectedChats.size} chat(s)? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const chatIds = Array.from(selectedChats);
              await realChatService.clearMultipleChats(chatIds, { 
                clearMessages: false, 
                clearMedia: false, 
                clearAll: true 
              });
              await loadChats();
              clearSelection();
              Alert.alert('Success', `${chatIds.length} chat(s) deleted`);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chats');
            }
          }
        }
      ]
    );
  }, [selectedChats, loadChats, clearSelection]);

  const handleBulkMute = useCallback(async () => {
    try {
      const chatIds = Array.from(selectedChats);
      // For now, just show success - mute functionality can be implemented later
      clearSelection();
      Alert.alert('Success', `${chatIds.length} chat(s) muted`);
    } catch (error) {
      Alert.alert('Error', 'Failed to mute chats');
    }
  }, [selectedChats, clearSelection]);

  useEffect(() => {
    loadChats();
  }, [loadChats]);

  return {
    // State
    chats,
    loading,
    selectedChats,
    searchQuery,
    filterType,
    sortBy,
    showBulkActions,
    showFilterModal,
    refreshing,
    // Actions
    loadChats,
    handleRefresh,
    toggleChatSelection,
    selectAllChats,
    clearSelection,
    handleBulkArchive,
    handleBulkDelete,
    handleBulkMute,
    setSearchQuery,
    setFilterType,
    setSortBy,
    setShowFilterModal,
  };
};
