import React from 'react';
import {
  View,
  Text,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EmptyStateProps {
  title?: string;
  subtitle?: string;
  icon?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = "No chats found",
  subtitle = "Try adjusting your search or criteria",
  icon = "chatbubbles-outline"
}) => {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    }}>
      <Ionicons name={icon as any} size={64} color="#CCC" />
      <Text style={{
        fontSize: 18,
        fontWeight: '600',
        color: '#666',
        marginTop: 16,
        textAlign: 'center',
      }}>
        {title}
      </Text>
      <Text style={{
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
      }}>
        {subtitle}
      </Text>
    </View>
  );
};
